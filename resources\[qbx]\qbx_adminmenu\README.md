![title](https://cdn.discordapp.com/attachments/1027004645912088686/1300263126243217438/adminmenu.jpg?ex=6720dca7&is=671f8b27&hm=6c218887153c78c50bfa0bd636c6b2b13686a2ea443a36645b81dbd7e8f37984&)

# qbx_adminmenu

Commands and a menu to provide tools to admins

## Features

- Player reports
- See player names & blips
- Change players model
- Take ownership of a vehicle or spawn a new vehicle to be an owner of
- See the character names in a specific radio channel
- Open the clothing menu
- modify a player's ACE permissions
- modify a character's data such as their radio channel, job, name, thirst level, etc.
- Give a player all the weapons
- Change a player's routing bucket
- See information about a player, including various identifiers
- kill/revive/freeze/kick/ban/teleport/noclip

## Player Commands
- /report <message> send a message to admins

## Admin Commands
- /admin opens the admin menu
- /noclip toggles noclip
- /names toggles player names
- /blips toggles player blips
- /setmodel <model> [id] change the model of the given player
- /admincar to Spawn a vehicle and take permanent ownership of it or take permanent ownership of the vehicle the admin is already sitting in
- /vec2 /vec3 /vec4 /heading copies the value to the clipboard


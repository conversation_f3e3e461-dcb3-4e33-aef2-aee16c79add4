local coreVehicles = exports.qbx_core:GetVehiclesByName()

function GenerateVehiclesSpawnMenu()
    local canUseMenu = lib.callback.await('qbx_admin:server:canUseMenu', false)
    if not canUseMenu then
        lib.showMenu('qbx_adminmenu_main_menu', MenuIndexes.qbx_adminmenu_main_menu)
        return
    end

    local indexedCategories = {}
    local categories = {}
    local vehs = {}
    for _, v in pairs(coreVehicles) do
        categories[v.category] = true
    end

    local categoryIndex = 1
    local newCategories = {}
    for k in pairs(categories) do
        newCategories[categoryIndex] = k
        categoryIndex += 1
    end

    categories = newCategories

    table.sort(categories, function(a, b)
        return a < b
    end)

    for i = 1, #categories do
        lib.setMenuOptions('qbx_adminmenu_spawn_vehicles_menu', {label = qbx.string.capitalize(categories[i]), args = {('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i])}}, i)

        lib.registerMenu({
            id = ('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i]),
            title = categories[i],
            position = 'top-right',
            onClose = function(keyPressed)
                CloseMenu(false, keyPressed, 'qbx_adminmenu_spawn_vehicles_menu')
            end,
            onSelected = function(selected)
                MenuIndexes[('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i])] = selected
            end,
            options = {}
        }, function(_, _, args)
            local vehNetId = lib.callback.await('qbx_admin:server:spawnVehicle', false, args[1])
            if not vehNetId then return end
            local veh
            repeat
                veh = NetToVeh(vehNetId)
                Wait(100)
            until DoesEntityExist(veh)
            TriggerEvent('qb-vehiclekeys:client:AddKeys', qbx.getVehiclePlate(veh))
            SetVehicleNeedsToBeHotwired(veh, false)
            SetVehicleHasBeenOwnedByPlayer(veh, true)
            SetEntityAsMissionEntity(veh, true, false)
            SetVehicleIsStolen(veh, false)
            SetVehicleIsWanted(veh, false)
            SetVehicleEngineOn(veh, true, true, true)
            SetPedIntoVehicle(cache.ped, veh, -1)
            SetVehicleOnGroundProperly(veh)
            SetVehicleRadioEnabled(veh, true)
            SetVehRadioStation(veh, 'OFF')
        end)
        indexedCategories[categories[i]] = 1
    end

    for k in pairs(coreVehicles) do
        vehs[#vehs + 1] = k
    end

    table.sort(vehs, function(a, b)
        return a < b
    end)

    for i = 1, #vehs do
        local v = coreVehicles[vehs[i]]
        lib.setMenuOptions(('qbx_adminmenu_spawn_vehicles_menu_%s'):format(v.category), {label = v.name, args = {v.model}}, indexedCategories[v.category])
        indexedCategories[v.category] += 1
    end

    lib.showMenu('qbx_adminmenu_spawn_vehicles_menu', MenuIndexes.qbx_adminmenu_spawn_vehicles_menu)
end

lib.registerMenu({
    id = 'qbx_adminmenu_vehicles_menu',
    title = 'Vehicles',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_vehicles_menu = selected
    end,
    options = {
        {label = 'Spawn Vehicle'},
        {label = 'Fix Vehicle', close = false},
        -- {label = 'Buy Vehicle', close = true},
        {label = 'Remove Vehicle', close = false},
        {label = 'Tune Vehicle', args = {'qbx_adminmenu_tune_vehicle_menu'}},
        {label = 'Change Plate'}
    }
}, function(selected, _, args)
    if selected == 1 then
        GenerateVehiclesSpawnMenu()
    elseif selected == 2 then
        ExecuteCommand('fix')
    -- elseif selected == 3 then
    --     ExecuteCommand('admincar')
    elseif selected == 4 then
        ExecuteCommand('dv')
    elseif selected == 5 then
        if args and args[1] then
            lib.showMenu(args[1], MenuIndexes[args[1]])
        else
            if not cache.vehicle then
                exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
                lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
                return
            end
            exports.qbx_customs:OpenMenu()
        end
    elseif selected == 6 then
        if not cache.vehicle then
            exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        local dialog = lib.inputDialog('Custom License Plate (Max. 8 characters)',  {'License Plate'})

        if not dialog or not dialog[1] or dialog[1] == '' then
            Wait(200)
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        if #dialog[1] > 8 then
            Wait(200)
            exports.qbx_core:Notify('You can only enter a maximum of 8 characters', 'error')
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        SetVehicleNumberPlateText(cache.vehicle, dialog[1])
        TriggerEvent('qb-vehiclekeys:client:AddKeys', dialog[1])
    end
end)

lib.registerMenu({
    id = 'qbx_adminmenu_spawn_vehicles_menu',
    title = 'Spawn Vehicle',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_spawn_vehicles_menu = selected
    end,
    options = {}
}, function(_, _, args)
    lib.showMenu(args[1], MenuIndexes[args[1]])
end)

lib.registerMenu({
    id = 'qbx_adminmenu_tune_vehicle_menu',
    title = 'Tune Vehicle',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_vehicles_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_tune_vehicle_menu = selected
    end,
    options = {
        {label = 'Engine', close = false},
        {label = 'Turbo', close = false},
        {label = 'Brakes', close = false},
        {label = 'Suspension', close = false},
        {label = 'Transmission', close = false},
        {label = 'Armor', close = false},
        {label = 'Exhaust', close = false},
        {label = 'Spoiler', close = false},
        {label = 'Bumpers', close = false},
        {label = 'Skirts', close = false},
        {label = 'Wheels', close = false},
        {label = 'Full Tune', close = false},
        {label = 'Open Customs Menu', close = false}
    }
}, function(selected)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
        return
    end

    local vehicle = cache.vehicle

    if selected == 1 then -- Engine
        SetVehicleMod(vehicle, 11, GetNumVehicleMods(vehicle, 11) - 1, false)
        exports.qbx_core:Notify('Engine upgraded to maximum level', 'success')
    elseif selected == 2 then -- Turbo
        ToggleVehicleMod(vehicle, 18, true)
        exports.qbx_core:Notify('Turbo installed', 'success')
    elseif selected == 3 then -- Brakes
        SetVehicleMod(vehicle, 12, GetNumVehicleMods(vehicle, 12) - 1, false)
        exports.qbx_core:Notify('Brakes upgraded to maximum level', 'success')
    elseif selected == 4 then -- Suspension
        SetVehicleMod(vehicle, 15, GetNumVehicleMods(vehicle, 15) - 1, false)
        exports.qbx_core:Notify('Suspension upgraded to maximum level', 'success')
    elseif selected == 5 then -- Transmission
        SetVehicleMod(vehicle, 13, GetNumVehicleMods(vehicle, 13) - 1, false)
        exports.qbx_core:Notify('Transmission upgraded to maximum level', 'success')
    elseif selected == 6 then -- Armor
        SetVehicleMod(vehicle, 16, GetNumVehicleMods(vehicle, 16) - 1, false)
        exports.qbx_core:Notify('Armor upgraded to maximum level', 'success')
    elseif selected == 7 then -- Exhaust
        SetVehicleMod(vehicle, 4, GetNumVehicleMods(vehicle, 4) - 1, false)
        exports.qbx_core:Notify('Exhaust upgraded', 'success')
    elseif selected == 8 then -- Spoiler
        SetVehicleMod(vehicle, 0, GetNumVehicleMods(vehicle, 0) - 1, false)
        exports.qbx_core:Notify('Spoiler installed', 'success')
    elseif selected == 9 then -- Bumpers
        SetVehicleMod(vehicle, 1, GetNumVehicleMods(vehicle, 1) - 1, false) -- Front bumper
        SetVehicleMod(vehicle, 2, GetNumVehicleMods(vehicle, 2) - 1, false) -- Rear bumper
        exports.qbx_core:Notify('Bumpers upgraded', 'success')
    elseif selected == 10 then -- Skirts
        SetVehicleMod(vehicle, 3, GetNumVehicleMods(vehicle, 3) - 1, false)
        exports.qbx_core:Notify('Skirts installed', 'success')
    elseif selected == 11 then -- Wheels
        SetVehicleMod(vehicle, 23, GetNumVehicleMods(vehicle, 23) - 1, false)
        exports.qbx_core:Notify('Wheels upgraded', 'success')
    elseif selected == 12 then -- Full Tune
        -- Performance upgrades
        SetVehicleMod(vehicle, 11, GetNumVehicleMods(vehicle, 11) - 1, false) -- Engine
        SetVehicleMod(vehicle, 12, GetNumVehicleMods(vehicle, 12) - 1, false) -- Brakes
        SetVehicleMod(vehicle, 13, GetNumVehicleMods(vehicle, 13) - 1, false) -- Transmission
        SetVehicleMod(vehicle, 15, GetNumVehicleMods(vehicle, 15) - 1, false) -- Suspension
        SetVehicleMod(vehicle, 16, GetNumVehicleMods(vehicle, 16) - 1, false) -- Armor
        ToggleVehicleMod(vehicle, 18, true) -- Turbo

        -- Visual upgrades
        for i = 0, 48 do
            local numMods = GetNumVehicleMods(vehicle, i)
            if numMods > 0 then
                SetVehicleMod(vehicle, i, numMods - 1, false)
            end
        end

        exports.qbx_core:Notify('Vehicle fully tuned!', 'success')
    elseif selected == 13 then -- Open Customs Menu
        exports.qbx_customs:OpenMenu()
    end

    lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
end)

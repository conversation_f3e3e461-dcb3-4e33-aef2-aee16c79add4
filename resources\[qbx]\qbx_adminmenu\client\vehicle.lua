local coreVehicles = exports.qbx_core:GetVehiclesByName()

function GenerateVehiclesSpawnMenu()
    local canUseMenu = lib.callback.await('qbx_admin:server:canUseMenu', false)
    if not canUseMenu then
        lib.showMenu('qbx_adminmenu_main_menu', MenuIndexes.qbx_adminmenu_main_menu)
        return
    end

    local indexedCategories = {}
    local categories = {}
    local vehs = {}
    for _, v in pairs(coreVehicles) do
        categories[v.category] = true
    end

    local categoryIndex = 1
    local newCategories = {}
    for k in pairs(categories) do
        newCategories[categoryIndex] = k
        categoryIndex += 1
    end

    categories = newCategories

    table.sort(categories, function(a, b)
        return a < b
    end)

    for i = 1, #categories do
        lib.setMenuOptions('qbx_adminmenu_spawn_vehicles_menu', {label = qbx.string.capitalize(categories[i]), args = {('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i])}}, i)

        lib.registerMenu({
            id = ('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i]),
            title = categories[i],
            position = 'top-right',
            onClose = function(keyPressed)
                CloseMenu(false, keyPressed, 'qbx_adminmenu_spawn_vehicles_menu')
            end,
            onSelected = function(selected)
                MenuIndexes[('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i])] = selected
            end,
            options = {}
        }, function(_, _, args)
            local vehNetId = lib.callback.await('qbx_admin:server:spawnVehicle', false, args[1])
            if not vehNetId then return end
            local veh
            repeat
                veh = NetToVeh(vehNetId)
                Wait(100)
            until DoesEntityExist(veh)
            TriggerEvent('qb-vehiclekeys:client:AddKeys', qbx.getVehiclePlate(veh))
            SetVehicleNeedsToBeHotwired(veh, false)
            SetVehicleHasBeenOwnedByPlayer(veh, true)
            SetEntityAsMissionEntity(veh, true, false)
            SetVehicleIsStolen(veh, false)
            SetVehicleIsWanted(veh, false)
            SetVehicleEngineOn(veh, true, true, true)
            SetPedIntoVehicle(cache.ped, veh, -1)
            SetVehicleOnGroundProperly(veh)
            SetVehicleRadioEnabled(veh, true)
            SetVehRadioStation(veh, 'OFF')
        end)
        indexedCategories[categories[i]] = 1
    end

    for k in pairs(coreVehicles) do
        vehs[#vehs + 1] = k
    end

    table.sort(vehs, function(a, b)
        return a < b
    end)

    for i = 1, #vehs do
        local v = coreVehicles[vehs[i]]
        lib.setMenuOptions(('qbx_adminmenu_spawn_vehicles_menu_%s'):format(v.category), {label = v.name, args = {v.model}}, indexedCategories[v.category])
        indexedCategories[v.category] += 1
    end

    lib.showMenu('qbx_adminmenu_spawn_vehicles_menu', MenuIndexes.qbx_adminmenu_spawn_vehicles_menu)
end

lib.registerMenu({
    id = 'qbx_adminmenu_vehicles_menu',
    title = 'Vehicles',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_vehicles_menu = selected
    end,
    options = {
        {label = 'Spawn Vehicle'},
        {label = 'Fix Vehicle', close = false},
        -- {label = 'Buy Vehicle', close = true},
        {label = 'Remove Vehicle', close = false},
        {label = 'Tune Vehicle', args = {'qbx_adminmenu_tune_vehicle_menu'}},
        {label = 'Change Plate'}
    }
}, function(selected, _, args)
    if selected == 1 then
        GenerateVehiclesSpawnMenu()
    elseif selected == 2 then
        ExecuteCommand('fix')
    -- elseif selected == 3 then
    --     ExecuteCommand('admincar')
    elseif selected == 4 then
        ExecuteCommand('dv')
    elseif selected == 5 then
        if args and args[1] then
            lib.showMenu(args[1], MenuIndexes[args[1]])
        else
            if not cache.vehicle then
                exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
                lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
                return
            end
            exports.qbx_customs:OpenMenu()
        end
    elseif selected == 6 then
        if not cache.vehicle then
            exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        local dialog = lib.inputDialog('Custom License Plate (Max. 8 characters)',  {'License Plate'})

        if not dialog or not dialog[1] or dialog[1] == '' then
            Wait(200)
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        if #dialog[1] > 8 then
            Wait(200)
            exports.qbx_core:Notify('You can only enter a maximum of 8 characters', 'error')
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        SetVehicleNumberPlateText(cache.vehicle, dialog[1])
        TriggerEvent('qb-vehiclekeys:client:AddKeys', dialog[1])
    end
end)

lib.registerMenu({
    id = 'qbx_adminmenu_spawn_vehicles_menu',
    title = 'Spawn Vehicle',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_spawn_vehicles_menu = selected
    end,
    options = {}
}, function(_, _, args)
    lib.showMenu(args[1], MenuIndexes[args[1]])
end)

lib.registerMenu({
    id = 'qbx_adminmenu_tune_vehicle_menu',
    title = 'Tune Vehicle',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_vehicles_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_tune_vehicle_menu = selected
    end,
    options = {
        {label = 'Engine', close = false},
        {label = 'Turbo', close = false},
        {label = 'Brakes', close = false},
        {label = 'Suspension', close = false},
        {label = 'Transmission', close = false},
        {label = 'Armor', close = false},
        {label = 'Exhaust', close = false},
        {label = 'Spoiler', close = false},
        {label = 'Bumpers', close = false},
        {label = 'Skirts', close = false},
        {label = 'Wheels', close = false},
        {label = 'Full Tune', close = false},
        {label = 'Open Customs Menu', close = false}
    }
}, function(selected)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
        return
    end

    local vehicle = cache.vehicle

    -- Ensure vehicle exists and is valid
    if not DoesEntityExist(vehicle) then
        exports.qbx_core:Notify('Vehicle not found', 'error')
        lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
        return
    end

    if selected == 1 then -- Engine
        local maxMods = GetNumVehicleMods(vehicle, 11)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 11, maxMods - 1, false)
            exports.qbx_core:Notify('Engine upgraded to maximum level', 'success')
        else
            exports.qbx_core:Notify('No engine upgrades available for this vehicle', 'error')
        end
    elseif selected == 2 then -- Turbo
        ToggleVehicleMod(vehicle, 18, true)
        exports.qbx_core:Notify('Turbo installed', 'success')
    elseif selected == 3 then -- Brakes
        local maxMods = GetNumVehicleMods(vehicle, 12)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 12, maxMods - 1, false)
            exports.qbx_core:Notify('Brakes upgraded to maximum level', 'success')
        else
            exports.qbx_core:Notify('No brake upgrades available for this vehicle', 'error')
        end
    elseif selected == 4 then -- Suspension
        local maxMods = GetNumVehicleMods(vehicle, 15)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 15, maxMods - 1, false)
            exports.qbx_core:Notify('Suspension upgraded to maximum level', 'success')
        else
            exports.qbx_core:Notify('No suspension upgrades available for this vehicle', 'error')
        end
    elseif selected == 5 then -- Transmission
        local maxMods = GetNumVehicleMods(vehicle, 13)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 13, maxMods - 1, false)
            exports.qbx_core:Notify('Transmission upgraded to maximum level', 'success')
        else
            exports.qbx_core:Notify('No transmission upgrades available for this vehicle', 'error')
        end
    elseif selected == 6 then -- Armor
        local maxMods = GetNumVehicleMods(vehicle, 16)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 16, maxMods - 1, false)
            exports.qbx_core:Notify('Armor upgraded to maximum level', 'success')
        else
            exports.qbx_core:Notify('No armor upgrades available for this vehicle', 'error')
        end
    elseif selected == 7 then -- Exhaust
        local maxMods = GetNumVehicleMods(vehicle, 4)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 4, maxMods - 1, false)
            exports.qbx_core:Notify('Exhaust upgraded', 'success')
        else
            exports.qbx_core:Notify('No exhaust upgrades available for this vehicle', 'error')
        end
    elseif selected == 8 then -- Spoiler
        local maxMods = GetNumVehicleMods(vehicle, 0)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 0, maxMods - 1, false)
            exports.qbx_core:Notify('Spoiler installed', 'success')
        else
            exports.qbx_core:Notify('No spoiler options available for this vehicle', 'error')
        end
    elseif selected == 9 then -- Bumpers
        local frontBumpers = GetNumVehicleMods(vehicle, 1)
        local rearBumpers = GetNumVehicleMods(vehicle, 2)
        local upgraded = false

        if frontBumpers > 0 then
            SetVehicleMod(vehicle, 1, frontBumpers - 1, false)
            upgraded = true
        end
        if rearBumpers > 0 then
            SetVehicleMod(vehicle, 2, rearBumpers - 1, false)
            upgraded = true
        end

        if upgraded then
            exports.qbx_core:Notify('Bumpers upgraded', 'success')
        else
            exports.qbx_core:Notify('No bumper upgrades available for this vehicle', 'error')
        end
    elseif selected == 10 then -- Skirts
        local maxMods = GetNumVehicleMods(vehicle, 3)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 3, maxMods - 1, false)
            exports.qbx_core:Notify('Skirts installed', 'success')
        else
            exports.qbx_core:Notify('No skirt options available for this vehicle', 'error')
        end
    elseif selected == 11 then -- Wheels
        local maxMods = GetNumVehicleMods(vehicle, 23)
        if maxMods > 0 then
            SetVehicleMod(vehicle, 23, maxMods - 1, false)
            exports.qbx_core:Notify('Wheels upgraded', 'success')
        else
            exports.qbx_core:Notify('No wheel upgrades available for this vehicle', 'error')
        end
    elseif selected == 12 then -- Full Tune
        local upgraded = false

        -- Performance upgrades
        local performanceMods = {
            {11, 'Engine'},
            {12, 'Brakes'},
            {13, 'Transmission'},
            {15, 'Suspension'},
            {16, 'Armor'}
        }

        for _, mod in pairs(performanceMods) do
            local maxMods = GetNumVehicleMods(vehicle, mod[1])
            if maxMods > 0 then
                SetVehicleMod(vehicle, mod[1], maxMods - 1, false)
                upgraded = true
            end
        end

        -- Turbo
        ToggleVehicleMod(vehicle, 18, true)
        upgraded = true

        if upgraded then
            exports.qbx_core:Notify('Vehicle performance fully tuned!', 'success')
        else
            exports.qbx_core:Notify('No performance upgrades available for this vehicle', 'error')
        end
    elseif selected == 13 then -- Open Customs Menu
        exports.qbx_customs:OpenMenu()
        return -- Don't show menu again since customs menu will take over
    end

    lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
end)

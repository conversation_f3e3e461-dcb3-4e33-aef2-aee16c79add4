local coreVehicles = exports.qbx_core:GetVehiclesByName()

function GenerateVehiclesSpawnMenu()
    local canUseMenu = lib.callback.await('qbx_admin:server:canUseMenu', false)
    if not canUseMenu then
        lib.showMenu('qbx_adminmenu_main_menu', MenuIndexes.qbx_adminmenu_main_menu)
        return
    end

    local indexedCategories = {}
    local categories = {}
    local vehs = {}
    for _, v in pairs(coreVehicles) do
        categories[v.category] = true
    end

    local categoryIndex = 1
    local newCategories = {}
    for k in pairs(categories) do
        newCategories[categoryIndex] = k
        categoryIndex += 1
    end

    categories = newCategories

    table.sort(categories, function(a, b)
        return a < b
    end)

    for i = 1, #categories do
        lib.setMenuOptions('qbx_adminmenu_spawn_vehicles_menu', {label = qbx.string.capitalize(categories[i]), args = {('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i])}}, i)

        lib.registerMenu({
            id = ('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i]),
            title = categories[i],
            position = 'top-right',
            onClose = function(keyPressed)
                CloseMenu(false, keyPressed, 'qbx_adminmenu_spawn_vehicles_menu')
            end,
            onSelected = function(selected)
                MenuIndexes[('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i])] = selected
            end,
            options = {}
        }, function(_, _, args)
            local vehNetId = lib.callback.await('qbx_admin:server:spawnVehicle', false, args[1])
            if not vehNetId then return end
            local veh
            repeat
                veh = NetToVeh(vehNetId)
                Wait(100)
            until DoesEntityExist(veh)
            TriggerEvent('qb-vehiclekeys:client:AddKeys', qbx.getVehiclePlate(veh))
            SetVehicleNeedsToBeHotwired(veh, false)
            SetVehicleHasBeenOwnedByPlayer(veh, true)
            SetEntityAsMissionEntity(veh, true, false)
            SetVehicleIsStolen(veh, false)
            SetVehicleIsWanted(veh, false)
            SetVehicleEngineOn(veh, true, true, true)
            SetPedIntoVehicle(cache.ped, veh, -1)
            SetVehicleOnGroundProperly(veh)
            SetVehicleRadioEnabled(veh, true)
            SetVehRadioStation(veh, 'OFF')
        end)
        indexedCategories[categories[i]] = 1
    end

    for k in pairs(coreVehicles) do
        vehs[#vehs + 1] = k
    end

    table.sort(vehs, function(a, b)
        return a < b
    end)

    for i = 1, #vehs do
        local v = coreVehicles[vehs[i]]
        lib.setMenuOptions(('qbx_adminmenu_spawn_vehicles_menu_%s'):format(v.category), {label = v.name, args = {v.model}}, indexedCategories[v.category])
        indexedCategories[v.category] += 1
    end

    lib.showMenu('qbx_adminmenu_spawn_vehicles_menu', MenuIndexes.qbx_adminmenu_spawn_vehicles_menu)
end

lib.registerMenu({
    id = 'qbx_adminmenu_vehicles_menu',
    title = 'Vehicles',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_vehicles_menu = selected
    end,
    options = {
        {label = 'Spawn Vehicle'},
        {label = 'Fix Vehicle', close = false},
        -- {label = 'Buy Vehicle', close = true},
        {label = 'Remove Vehicle', close = false},
        -- {label = 'Tune Vehicle', args = {'qbx_adminmenu_tune_vehicle_menu'}},
        {label = 'Change Plate'}
    }
}, function(selected, _, args)
    if selected == 1 then
        GenerateVehiclesSpawnMenu()
    elseif selected == 2 then
        ExecuteCommand('fix')
    -- elseif selected == 3 then
    --     ExecuteCommand('admincar')
    elseif selected == 4 then
        ExecuteCommand('dv')
    -- elseif selected == 5 then
    --     if args and args[1] then
    --         lib.showMenu(args[1], MenuIndexes[args[1]])
    --     else
    --         if not cache.vehicle then
    --             exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
    --             lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
    --             return
    --         end
    --         exports.qbx_customs:OpenMenu()
    --     end
    elseif selected == 5 then
        if not cache.vehicle then
            exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        local dialog = lib.inputDialog('Custom License Plate (Max. 8 characters)',  {'License Plate'})

        if not dialog or not dialog[1] or dialog[1] == '' then
            Wait(200)
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        if #dialog[1] > 8 then
            Wait(200)
            exports.qbx_core:Notify('You can only enter a maximum of 8 characters', 'error')
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        SetVehicleNumberPlateText(cache.vehicle, dialog[1])
        TriggerEvent('qb-vehiclekeys:client:AddKeys', dialog[1])
    end
end)

lib.registerMenu({
    id = 'qbx_adminmenu_spawn_vehicles_menu',
    title = 'Spawn Vehicle',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_spawn_vehicles_menu = selected
    end,
    options = {}
}, function(_, _, args)
    lib.showMenu(args[1], MenuIndexes[args[1]])
end)

lib.registerMenu({
    id = 'qbx_adminmenu_tune_vehicle_menu',
    title = 'Tune Vehicle',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_tune_vehicle_menu = selected
    end,
    options = {
        {label = 'Max Performance', close = false},
        {label = 'Open Customs Menu', close = false},
        {label = 'Custom Tuning', args = {'qbx_adminmenu_custom_tuning_menu'}}
    }
}, function(selected, _, args)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
        return
    end

    if selected == 1 then -- Max Performance
        ExecuteCommand('fix')
        Wait(100)
        exports.qbx_core:Notify('Vehicle performance maximized!', 'success')
        lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
    elseif selected == 2 then -- Open Customs Menu
        exports.qbx_customs:OpenMenu()
        return -- Don't show menu again since customs menu will take over
    elseif selected == 3 then -- Custom Tuning
        if args and args[1] then
            lib.showMenu(args[1], MenuIndexes[args[1]])
        end
    end
end)

lib.registerMenu({
    id = 'qbx_adminmenu_custom_tuning_menu',
    title = 'Custom Tuning',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_tune_vehicle_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_custom_tuning_menu = selected
    end,
    options = {
        {label = 'Engine', args = {'qbx_adminmenu_engine_tuning_menu'}},
        {label = 'Turbo', args = {'qbx_adminmenu_turbo_tuning_menu'}},
        {label = 'Brakes', args = {'qbx_adminmenu_brakes_tuning_menu'}},
        {label = 'Suspension', args = {'qbx_adminmenu_suspension_tuning_menu'}},
        {label = 'Transmission', args = {'qbx_adminmenu_transmission_tuning_menu'}},
        {label = 'Armor', args = {'qbx_adminmenu_armor_tuning_menu'}}
    }
}, function(selected, _, args)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        lib.showMenu('qbx_adminmenu_custom_tuning_menu', MenuIndexes.qbx_adminmenu_custom_tuning_menu)
        return
    end

    if args and args[1] then
        lib.showMenu(args[1], MenuIndexes[args[1]])
    end
end)

-- Engine Tuning Menu
lib.registerMenu({
    id = 'qbx_adminmenu_engine_tuning_menu',
    title = 'Engine Tuning',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_custom_tuning_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_engine_tuning_menu = selected
    end,
    options = {
        {label = 'Engine Level 1', close = false},
        {label = 'Engine Level 2', close = false},
        {label = 'Engine Level 3', close = false},
        {label = 'Remove Engine Upgrade', close = false}
    }
}, function(selected)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        return
    end

    local vehicle = cache.vehicle

    if selected == 1 then
        SetVehicleMod(vehicle, 11, 0, false)
        exports.qbx_core:Notify('Engine upgraded to Level 1', 'success')
    elseif selected == 2 then
        SetVehicleMod(vehicle, 11, 1, false)
        exports.qbx_core:Notify('Engine upgraded to Level 2', 'success')
    elseif selected == 3 then
        SetVehicleMod(vehicle, 11, 2, false)
        exports.qbx_core:Notify('Engine upgraded to Level 3', 'success')
    elseif selected == 4 then
        SetVehicleMod(vehicle, 11, -1, false)
        exports.qbx_core:Notify('Engine upgrade removed', 'success')
    end

    lib.showMenu('qbx_adminmenu_engine_tuning_menu', MenuIndexes.qbx_adminmenu_engine_tuning_menu)
end)

-- Turbo Tuning Menu
lib.registerMenu({
    id = 'qbx_adminmenu_turbo_tuning_menu',
    title = 'Turbo Tuning',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_custom_tuning_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_turbo_tuning_menu = selected
    end,
    options = {
        {label = 'Install Turbo', close = false},
        {label = 'Remove Turbo', close = false}
    }
}, function(selected)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        return
    end

    local vehicle = cache.vehicle

    if selected == 1 then
        ToggleVehicleMod(vehicle, 18, true)
        exports.qbx_core:Notify('Turbo installed', 'success')
    elseif selected == 2 then
        ToggleVehicleMod(vehicle, 18, false)
        exports.qbx_core:Notify('Turbo removed', 'success')
    end

    lib.showMenu('qbx_adminmenu_turbo_tuning_menu', MenuIndexes.qbx_adminmenu_turbo_tuning_menu)
end)

-- Brakes Tuning Menu
lib.registerMenu({
    id = 'qbx_adminmenu_brakes_tuning_menu',
    title = 'Brakes Tuning',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_custom_tuning_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_brakes_tuning_menu = selected
    end,
    options = {
        {label = 'Brakes Level 1', close = false},
        {label = 'Brakes Level 2', close = false},
        {label = 'Brakes Level 3', close = false},
        {label = 'Remove Brakes Upgrade', close = false}
    }
}, function(selected)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        return
    end

    local vehicle = cache.vehicle

    if selected == 1 then
        SetVehicleMod(vehicle, 12, 0, false)
        exports.qbx_core:Notify('Brakes upgraded to Level 1', 'success')
    elseif selected == 2 then
        SetVehicleMod(vehicle, 12, 1, false)
        exports.qbx_core:Notify('Brakes upgraded to Level 2', 'success')
    elseif selected == 3 then
        SetVehicleMod(vehicle, 12, 2, false)
        exports.qbx_core:Notify('Brakes upgraded to Level 3', 'success')
    elseif selected == 4 then
        SetVehicleMod(vehicle, 12, -1, false)
        exports.qbx_core:Notify('Brakes upgrade removed', 'success')
    end

    lib.showMenu('qbx_adminmenu_brakes_tuning_menu', MenuIndexes.qbx_adminmenu_brakes_tuning_menu)
end)

-- Suspension Tuning Menu
lib.registerMenu({
    id = 'qbx_adminmenu_suspension_tuning_menu',
    title = 'Suspension Tuning',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_custom_tuning_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_suspension_tuning_menu = selected
    end,
    options = {
        {label = 'Suspension Level 1', close = false},
        {label = 'Suspension Level 2', close = false},
        {label = 'Suspension Level 3', close = false},
        {label = 'Remove Suspension Upgrade', close = false}
    }
}, function(selected)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        return
    end

    local vehicle = cache.vehicle

    if selected == 1 then
        SetVehicleMod(vehicle, 15, 0, false)
        exports.qbx_core:Notify('Suspension upgraded to Level 1', 'success')
    elseif selected == 2 then
        SetVehicleMod(vehicle, 15, 1, false)
        exports.qbx_core:Notify('Suspension upgraded to Level 2', 'success')
    elseif selected == 3 then
        SetVehicleMod(vehicle, 15, 2, false)
        exports.qbx_core:Notify('Suspension upgraded to Level 3', 'success')
    elseif selected == 4 then
        SetVehicleMod(vehicle, 15, -1, false)
        exports.qbx_core:Notify('Suspension upgrade removed', 'success')
    end

    lib.showMenu('qbx_adminmenu_suspension_tuning_menu', MenuIndexes.qbx_adminmenu_suspension_tuning_menu)
end)

-- Transmission Tuning Menu
lib.registerMenu({
    id = 'qbx_adminmenu_transmission_tuning_menu',
    title = 'Transmission Tuning',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_custom_tuning_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_transmission_tuning_menu = selected
    end,
    options = {
        {label = 'Transmission Level 1', close = false},
        {label = 'Transmission Level 2', close = false},
        {label = 'Transmission Level 3', close = false},
        {label = 'Remove Transmission Upgrade', close = false}
    }
}, function(selected)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        return
    end

    local vehicle = cache.vehicle

    if selected == 1 then
        SetVehicleMod(vehicle, 13, 0, false)
        exports.qbx_core:Notify('Transmission upgraded to Level 1', 'success')
    elseif selected == 2 then
        SetVehicleMod(vehicle, 13, 1, false)
        exports.qbx_core:Notify('Transmission upgraded to Level 2', 'success')
    elseif selected == 3 then
        SetVehicleMod(vehicle, 13, 2, false)
        exports.qbx_core:Notify('Transmission upgraded to Level 3', 'success')
    elseif selected == 4 then
        SetVehicleMod(vehicle, 13, -1, false)
        exports.qbx_core:Notify('Transmission upgrade removed', 'success')
    end

    lib.showMenu('qbx_adminmenu_transmission_tuning_menu', MenuIndexes.qbx_adminmenu_transmission_tuning_menu)
end)

-- Armor Tuning Menu
lib.registerMenu({
    id = 'qbx_adminmenu_armor_tuning_menu',
    title = 'Armor Tuning',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_custom_tuning_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_armor_tuning_menu = selected
    end,
    options = {
        {label = 'Armor Level 1', close = false},
        {label = 'Armor Level 2', close = false},
        {label = 'Armor Level 3', close = false},
        {label = 'Remove Armor Upgrade', close = false}
    }
}, function(selected)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        return
    end

    local vehicle = cache.vehicle

    if selected == 1 then
        SetVehicleMod(vehicle, 16, 0, false)
        exports.qbx_core:Notify('Armor upgraded to Level 1', 'success')
    elseif selected == 2 then
        SetVehicleMod(vehicle, 16, 1, false)
        exports.qbx_core:Notify('Armor upgraded to Level 2', 'success')
    elseif selected == 3 then
        SetVehicleMod(vehicle, 16, 2, false)
        exports.qbx_core:Notify('Armor upgraded to Level 3', 'success')
    elseif selected == 4 then
        SetVehicleMod(vehicle, 16, -1, false)
        exports.qbx_core:Notify('Armor upgrade removed', 'success')
    end

    lib.showMenu('qbx_adminmenu_armor_tuning_menu', MenuIndexes.qbx_adminmenu_armor_tuning_menu)
end)

local coreVehicles = exports.qbx_core:GetVehiclesByName()

function GenerateVehiclesSpawnMenu()
    local canUseMenu = lib.callback.await('qbx_admin:server:canUseMenu', false)
    if not canUseMenu then
        lib.showMenu('qbx_adminmenu_main_menu', MenuIndexes.qbx_adminmenu_main_menu)
        return
    end

    local indexedCategories = {}
    local categories = {}
    local vehs = {}
    for _, v in pairs(coreVehicles) do
        categories[v.category] = true
    end

    local categoryIndex = 1
    local newCategories = {}
    for k in pairs(categories) do
        newCategories[categoryIndex] = k
        categoryIndex += 1
    end

    categories = newCategories

    table.sort(categories, function(a, b)
        return a < b
    end)

    for i = 1, #categories do
        lib.setMenuOptions('qbx_adminmenu_spawn_vehicles_menu', {label = qbx.string.capitalize(categories[i]), args = {('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i])}}, i)

        lib.registerMenu({
            id = ('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i]),
            title = categories[i],
            position = 'top-right',
            onClose = function(keyPressed)
                CloseMenu(false, keyPressed, 'qbx_adminmenu_spawn_vehicles_menu')
            end,
            onSelected = function(selected)
                MenuIndexes[('qbx_adminmenu_spawn_vehicles_menu_%s'):format(categories[i])] = selected
            end,
            options = {}
        }, function(_, _, args)
            local vehNetId = lib.callback.await('qbx_admin:server:spawnVehicle', false, args[1])
            if not vehNetId then return end
            local veh
            repeat
                veh = NetToVeh(vehNetId)
                Wait(100)
            until DoesEntityExist(veh)
            TriggerEvent('qb-vehiclekeys:client:AddKeys', qbx.getVehiclePlate(veh))
            SetVehicleNeedsToBeHotwired(veh, false)
            SetVehicleHasBeenOwnedByPlayer(veh, true)
            SetEntityAsMissionEntity(veh, true, false)
            SetVehicleIsStolen(veh, false)
            SetVehicleIsWanted(veh, false)
            SetVehicleEngineOn(veh, true, true, true)
            SetPedIntoVehicle(cache.ped, veh, -1)
            SetVehicleOnGroundProperly(veh)
            SetVehicleRadioEnabled(veh, true)
            SetVehRadioStation(veh, 'OFF')
        end)
        indexedCategories[categories[i]] = 1
    end

    for k in pairs(coreVehicles) do
        vehs[#vehs + 1] = k
    end

    table.sort(vehs, function(a, b)
        return a < b
    end)

    for i = 1, #vehs do
        local v = coreVehicles[vehs[i]]
        lib.setMenuOptions(('qbx_adminmenu_spawn_vehicles_menu_%s'):format(v.category), {label = v.name, args = {v.model}}, indexedCategories[v.category])
        indexedCategories[v.category] += 1
    end

    lib.showMenu('qbx_adminmenu_spawn_vehicles_menu', MenuIndexes.qbx_adminmenu_spawn_vehicles_menu)
end

lib.registerMenu({
    id = 'qbx_adminmenu_vehicles_menu',
    title = 'Vehicles',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_vehicles_menu = selected
    end,
    options = {
        {label = 'Spawn Vehicle'},
        {label = 'Fix Vehicle', close = false},
        -- {label = 'Buy Vehicle', close = true},
        {label = 'Remove Vehicle', close = false},
        -- {label = 'Tune Vehicle', args = {'qbx_adminmenu_tune_vehicle_menu'}},
        {label = 'Change Plate'}
    }
}, function(selected, _, args)
    if selected == 1 then
        GenerateVehiclesSpawnMenu()
    elseif selected == 2 then
        ExecuteCommand('fix')
    -- elseif selected == 3 then
    --     ExecuteCommand('admincar')
    elseif selected == 4 then
        ExecuteCommand('dv')
    -- elseif selected == 5 then
    --     if args and args[1] then
    --         lib.showMenu(args[1], MenuIndexes[args[1]])
    --     else
    --         if not cache.vehicle then
    --             exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
    --             lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
    --             return
    --         end
    --         exports.qbx_customs:OpenMenu()
    --     end
    elseif selected == 5 then
        if not cache.vehicle then
            exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        local dialog = lib.inputDialog('Custom License Plate (Max. 8 characters)',  {'License Plate'})

        if not dialog or not dialog[1] or dialog[1] == '' then
            Wait(200)
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        if #dialog[1] > 8 then
            Wait(200)
            exports.qbx_core:Notify('You can only enter a maximum of 8 characters', 'error')
            lib.showMenu('qbx_adminmenu_vehicles_menu', MenuIndexes.qbx_adminmenu_vehicles_menu)
            return
        end

        SetVehicleNumberPlateText(cache.vehicle, dialog[1])
        TriggerEvent('qb-vehiclekeys:client:AddKeys', dialog[1])
    end
end)

lib.registerMenu({
    id = 'qbx_adminmenu_spawn_vehicles_menu',
    title = 'Spawn Vehicle',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_spawn_vehicles_menu = selected
    end,
    options = {}
}, function(_, _, args)
    lib.showMenu(args[1], MenuIndexes[args[1]])
end)

lib.registerMenu({
    id = 'qbx_adminmenu_tune_vehicle_menu',
    title = 'Tune Vehicle',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_main_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_tune_vehicle_menu = selected
    end,
    options = {
        {label = 'Max Performance', close = false},
        {label = 'Custom Tuning', args = {'qbx_adminmenu_custom_tuning_menu'}}
    }
}, function(selected, _, args)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
        return
    end

    if selected == 1 then -- Max Performance
        ExecuteCommand('fix')
        Wait(100)
        exports.qbx_core:Notify('Vehicle performance maximized!', 'success')
        lib.showMenu('qbx_adminmenu_tune_vehicle_menu', MenuIndexes.qbx_adminmenu_tune_vehicle_menu)
    elseif selected == 2 then -- Custom Tuning
        if args and args[1] then
            lib.showMenu(args[1], MenuIndexes[args[1]])
        end
    end
end)

lib.registerMenu({
    id = 'qbx_adminmenu_custom_tuning_menu',
    title = 'Custom Tuning',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_tune_vehicle_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_custom_tuning_menu = selected
    end,
    options = {
        {label = 'Engine', icon = 'fas fa-cog', values = {'Stock', 'Level 1', 'Level 2', 'Level 3'}, close = false},
        {label = 'Turbo', icon = 'fas fa-wind', values = {'Off', 'On'}, close = false},
        {label = 'Brakes', icon = 'fas fa-stop-circle', values = {'Stock', 'Level 1', 'Level 2', 'Level 3'}, close = false},
        {label = 'Suspension', icon = 'fas fa-car', values = {'Stock', 'Level 1', 'Level 2', 'Level 3'}, close = false},
        {label = 'Transmission', icon = 'fas fa-tachometer-alt', values = {'Stock', 'Level 1', 'Level 2', 'Level 3'}, close = false},
        {label = 'Armor', icon = 'fas fa-shield-alt', values = {'Stock', 'Level 1', 'Level 2', 'Level 3'}, close = false},
        {label = 'Cosmetics', icon = 'fas fa-palette', args = {'qbx_adminmenu_cosmetics_menu'}}
    }
}, function(selected, scrollIndex, args)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        lib.showMenu('qbx_adminmenu_custom_tuning_menu', MenuIndexes.qbx_adminmenu_custom_tuning_menu)
        return
    end

    local vehicle = cache.vehicle

    if selected == 1 then -- Engine
        local engineLevel = scrollIndex - 1 -- 0 = Stock, 1 = Level 1, etc.
        if engineLevel == 0 then
            SetVehicleMod(vehicle, 11, -1, false)
            exports.qbx_core:Notify('Engine set to Stock', 'success')
        else
            SetVehicleMod(vehicle, 11, engineLevel - 1, false)
            exports.qbx_core:Notify('Engine set to Level ' .. engineLevel, 'success')
        end
    elseif selected == 2 then -- Turbo
        local turboOn = scrollIndex == 2 -- 1 = Off, 2 = On
        ToggleVehicleMod(vehicle, 18, turboOn)
        exports.qbx_core:Notify('Turbo ' .. (turboOn and 'installed' or 'removed'), 'success')
    elseif selected == 3 then -- Brakes
        local brakeLevel = scrollIndex - 1
        if brakeLevel == 0 then
            SetVehicleMod(vehicle, 12, -1, false)
            exports.qbx_core:Notify('Brakes set to Stock', 'success')
        else
            SetVehicleMod(vehicle, 12, brakeLevel - 1, false)
            exports.qbx_core:Notify('Brakes set to Level ' .. brakeLevel, 'success')
        end
    elseif selected == 4 then -- Suspension
        local suspensionLevel = scrollIndex - 1
        if suspensionLevel == 0 then
            SetVehicleMod(vehicle, 15, -1, false)
            exports.qbx_core:Notify('Suspension set to Stock', 'success')
        else
            SetVehicleMod(vehicle, 15, suspensionLevel - 1, false)
            exports.qbx_core:Notify('Suspension set to Level ' .. suspensionLevel, 'success')
        end
    elseif selected == 5 then -- Transmission
        local transmissionLevel = scrollIndex - 1
        if transmissionLevel == 0 then
            SetVehicleMod(vehicle, 13, -1, false)
            exports.qbx_core:Notify('Transmission set to Stock', 'success')
        else
            SetVehicleMod(vehicle, 13, transmissionLevel - 1, false)
            exports.qbx_core:Notify('Transmission set to Level ' .. transmissionLevel, 'success')
        end
    elseif selected == 6 then -- Armor
        local armorLevel = scrollIndex - 1
        if armorLevel == 0 then
            SetVehicleMod(vehicle, 16, -1, false)
            exports.qbx_core:Notify('Armor set to Stock', 'success')
        else
            SetVehicleMod(vehicle, 16, armorLevel - 1, false)
            exports.qbx_core:Notify('Armor set to Level ' .. armorLevel, 'success')
        end
    elseif selected == 7 then -- Cosmetics
        if args and args[1] then
            lib.showMenu(args[1], MenuIndexes[args[1]])
        end
    end

    if selected ~= 7 then -- Don't refresh menu if going to cosmetics
        lib.showMenu('qbx_adminmenu_custom_tuning_menu', MenuIndexes.qbx_adminmenu_custom_tuning_menu)
    end
end)

-- Cosmetics Menu
lib.registerMenu({
    id = 'qbx_adminmenu_cosmetics_menu',
    title = 'Vehicle Cosmetics',
    position = 'top-right',
    onClose = function(keyPressed)
        CloseMenu(false, keyPressed, 'qbx_adminmenu_custom_tuning_menu')
    end,
    onSelected = function(selected)
        MenuIndexes.qbx_adminmenu_cosmetics_menu = selected
    end,
    options = {
        {label = 'Spoiler', icon = 'fas fa-car-side', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Front Bumper', icon = 'fas fa-car-crash', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Rear Bumper', icon = 'fas fa-car-crash', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Side Skirts', icon = 'fas fa-car', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Exhaust', icon = 'fas fa-wind', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Hood', icon = 'fas fa-car', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Fenders', icon = 'fas fa-car-side', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Grille', icon = 'fas fa-th-large', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Roof', icon = 'fas fa-home', values = {'Stock', 'Option 1', 'Option 2', 'Option 3'}, close = false},
        {label = 'Wheels', icon = 'fas fa-circle', values = {'Stock', 'Sport', 'Muscle', 'Lowrider', 'SUV', 'Offroad', 'Tuner', 'High End'}, close = false}
    }
}, function(selected, scrollIndex, args)
    if not cache.vehicle then
        exports.qbx_core:Notify('You have to be in a vehicle, to use this', 'error')
        lib.showMenu('qbx_adminmenu_cosmetics_menu', MenuIndexes.qbx_adminmenu_cosmetics_menu)
        return
    end

    local vehicle = cache.vehicle
    local modValue = scrollIndex - 1 -- Convert to 0-based index

    if modValue == 0 then
        modValue = -1 -- Stock option
    else
        modValue = modValue - 1 -- Adjust for actual mod values
    end

    if selected == 1 then -- Spoiler
        SetVehicleMod(vehicle, 0, modValue, false)
        exports.qbx_core:Notify('Spoiler changed', 'success')
    elseif selected == 2 then -- Front Bumper
        SetVehicleMod(vehicle, 1, modValue, false)
        exports.qbx_core:Notify('Front Bumper changed', 'success')
    elseif selected == 3 then -- Rear Bumper
        SetVehicleMod(vehicle, 2, modValue, false)
        exports.qbx_core:Notify('Rear Bumper changed', 'success')
    elseif selected == 4 then -- Side Skirts
        SetVehicleMod(vehicle, 3, modValue, false)
        exports.qbx_core:Notify('Side Skirts changed', 'success')
    elseif selected == 5 then -- Exhaust
        SetVehicleMod(vehicle, 4, modValue, false)
        exports.qbx_core:Notify('Exhaust changed', 'success')
    elseif selected == 6 then -- Hood
        SetVehicleMod(vehicle, 7, modValue, false)
        exports.qbx_core:Notify('Hood changed', 'success')
    elseif selected == 7 then -- Fenders
        SetVehicleMod(vehicle, 8, modValue, false)
        exports.qbx_core:Notify('Fenders changed', 'success')
    elseif selected == 8 then -- Grille
        SetVehicleMod(vehicle, 6, modValue, false)
        exports.qbx_core:Notify('Grille changed', 'success')
    elseif selected == 9 then -- Roof
        SetVehicleMod(vehicle, 10, modValue, false)
        exports.qbx_core:Notify('Roof changed', 'success')
    elseif selected == 10 then -- Wheels
        if scrollIndex == 1 then -- Stock
            SetVehicleMod(vehicle, 23, -1, false)
            exports.qbx_core:Notify('Wheels set to Stock', 'success')
        else
            -- Set wheel type first, then apply a mod
            SetVehicleWheelType(vehicle, scrollIndex - 2) -- Wheel types: 0=Sport, 1=Muscle, etc.
            SetVehicleMod(vehicle, 23, 0, false) -- Apply first wheel of that type
            local wheelTypes = {'Sport', 'Muscle', 'Lowrider', 'SUV', 'Offroad', 'Tuner', 'High End'}
            exports.qbx_core:Notify('Wheels set to ' .. wheelTypes[scrollIndex - 1], 'success')
        end
    end

    lib.showMenu('qbx_adminmenu_cosmetics_menu', MenuIndexes.qbx_adminmenu_cosmetics_menu)
end)


